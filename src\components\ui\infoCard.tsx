import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-card rounded-xl p-6 border border-border relative transition-all duration-300 hover:shadow-lg hover:border-primary-200 hover:-translate-y-1 group ${className}`}
    style={{
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      ...style
    }}
  >
    <View className="flex items-center justify-between">
      <View className="flex-1">
        <View className="flex items-center gap-3 mb-3">
          {icon && (
            <View
              className={`p-2.5 rounded-lg bg-gradient-to-br transition-all duration-300 ${
                iconStyle ? iconStyle : "from-blue-50 to-indigo-50 text-blue-600 dark:from-blue-900/20 dark:to-indigo-900/20 dark:text-blue-400"
              }`}
            >
              {icon}
            </View>
          )}
          <Text
            as="h3"
            className={`text-sm font-medium text-gray-600 dark:text-gray-400 ${
              titleStyle ? titleStyle : ""
            }`}
          >
            {label}
          </Text>
        </View>
        <Text
          weight="font-bold"
          className={`text-2xl font-bold text-gray-900 dark:text-white leading-none ${
            isLink ? "text-primary cursor-pointer hover:underline" : ""
          } ${valueStyle ? valueStyle : ""}`}
        >
          {value}
        </Text>
        {subValue && (
          <Text
            className={`text-xs text-gray-500 dark:text-gray-400 mt-2 ${
              subValueStyle ? subValueStyle : ""
            }`}
          >
            {subValue}
          </Text>
        )}
      </View>
    </View>
  </View>
);

export default InfoCard;
