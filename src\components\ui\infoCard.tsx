import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-white dark:bg-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all duration-200 relative ${className}`}
    style={style}
  >
    <View className="flex items-start justify-between">
      <View className="flex-1">
        <Text
          as="h3"
          className={`text-sm font-medium text-gray-600 dark:text-gray-400 mb-3 ${
            titleStyle ? titleStyle : ""
          }`}
        >
          {label}
        </Text>
        <Text
          weight="font-bold"
          className={`text-2xl font-bold text-gray-900 dark:text-white ${
            isLink ? "text-primary cursor-pointer hover:underline" : ""
          } ${valueStyle ? valueStyle : ""}`}
        >
          {value}
        </Text>
        {subValue && (
          <Text
            className={`text-sm text-gray-500 dark:text-gray-400 mt-1 ${
              subValueStyle ? subValueStyle : ""
            }`}
          >
            {subValue}
          </Text>
        )}
      </View>
      {icon && (
        <View
          className={`p-3 rounded-lg bg-blue-50 text-blue-600 dark:bg-blue-500/20 dark:text-blue-400 ${
            iconStyle ? iconStyle : ""
          }`}
        >
          {icon}
        </View>
      )}
    </View>
  </View>
);

export default InfoCard;
