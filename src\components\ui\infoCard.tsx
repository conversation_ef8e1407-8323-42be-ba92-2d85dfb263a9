import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-gradient-to-br from-card via-card to-primary-50/30 dark:to-primary-900/10 rounded-2xl p-8 border border-border/50 relative transition-all duration-500 hover:shadow-2xl hover:border-primary-300 hover:-translate-y-2 hover:scale-105 group backdrop-blur-sm ${className}`}
    style={{
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      ...style
    }}
  >
    <View className="flex items-center justify-between">
      <View className="flex-1">
        <View className="flex items-center gap-4 mb-4">
          {icon && (
            <View
              className={`p-4 rounded-2xl bg-gradient-to-br transition-all duration-500 group-hover:scale-110 shadow-lg ${
                iconStyle ? iconStyle : "from-primary-100 to-secondary-100 text-primary-600 dark:from-primary-900/30 dark:to-secondary-900/30 dark:text-primary-400"
              }`}
            >
              {icon}
            </View>
          )}
          <Text
            as="h3"
            className={`text-sm font-semibold text-muted-foreground uppercase tracking-wider ${
              titleStyle ? titleStyle : ""
            }`}
          >
            {label}
          </Text>
        </View>
        <Text
          weight="font-bold"
          className={`text-4xl font-bold text-foreground leading-none transition-all duration-300 group-hover:scale-105 ${
            isLink ? "text-primary cursor-pointer hover:underline" : ""
          } ${valueStyle ? valueStyle : ""}`}
        >
          {value}
        </Text>
        {subValue && (
          <Text
            className={`text-xs text-gray-500 dark:text-gray-400 mt-2 ${
              subValueStyle ? subValueStyle : ""
            }`}
          >
            {subValue}
          </Text>
        )}
      </View>
    </View>
  </View>
);

export default InfoCard;
