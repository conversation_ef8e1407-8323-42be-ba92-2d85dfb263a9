import React from "react";
import Text from "../text";
import View from "../view";

interface InfoCardProps {
  label: string;
  value: string | React.ReactNode;
  subValue?: string;
  icon?: React.ReactNode;
  isLink?: boolean;
  className?: string;
  style?: React.CSSProperties;
  titleStyle?: string;
  valueStyle?: string;
  subValueStyle?: string;
  iconStyle?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  label,
  value,
  subValue = "",
  icon,
  isLink = false,
  className,
  style,
  titleStyle,
  valueStyle,
  subValueStyle,
  iconStyle,
}: InfoCardProps) => (
  <View
    className={`bg-white dark:bg-card rounded-2xl p-6 shadow-lg border border-gray-100 dark:border-gray-800 relative transition-all duration-300 hover:shadow-xl hover:-translate-y-1 group ${className}`}
    style={style}
  >
    <View className="flex items-start justify-between mb-4">
      <View className="flex-1">
        <Text
          as="h3"
          className={`text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 ${
            titleStyle ? titleStyle : ""
          }`}
        >
          {label}
        </Text>
        <Text
          weight="font-bold"
          className={`text-3xl font-bold text-gray-900 dark:text-white ${
            isLink ? "text-primary cursor-pointer hover:underline" : ""
          } ${valueStyle ? valueStyle : ""}`}
        >
          {value}
        </Text>
        {subValue && (
          <Text
            className={`text-sm text-gray-500 dark:text-gray-400 mt-1 ${
              subValueStyle ? subValueStyle : ""
            }`}
          >
            {subValue}
          </Text>
        )}
      </View>
      {icon && (
        <View
          className={`p-3 rounded-xl bg-gradient-to-br from-primary-50 to-primary-100 text-primary-600 dark:from-primary-900/20 dark:to-primary-800/20 dark:text-primary-400 transition-all duration-300 group-hover:scale-110 ${
            iconStyle ? iconStyle : ""
          }`}
        >
          {icon}
        </View>
      )}
    </View>
  </View>
);

export default InfoCard;
