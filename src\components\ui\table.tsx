import * as React from "react"
import View from "../view"
 
const Table = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement>
>(({ className, ...props }, ref) => (
<View className="relative w-full overflow-auto">
<table
      ref={ref}
      className={`w-full caption-bottom text-sm ${className || ''}`}
      {...props}
    />
</View>
))
Table.displayName = "Table"
 
const TableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
<thead ref={ref} className={`[&_tr]:border-b ${className || ''}`} {...props} />
))
TableHeader.displayName = "TableHeader"
 
const TableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
<tbody
    ref={ref}
    className={` ${className || ''}`}
    {...props}
  />
))
TableBody.displayName = "TableBody"
 
const TableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
<tfoot
    ref={ref}
    className={`border-t bg-muted/50 font-medium [&>tr]:last:border-b-0 ${className || ''}`}
    {...props}
  />
))
TableFooter.displayName = "TableFooter"
 
const TableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
<tr
    ref={ref}
    className={`border-b border-border transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted ${className || ''}`}
    {...props}
  />
))
TableRow.displayName = "TableRow"
 
const TableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
<th
    ref={ref}
    className={`h-14 px-6 text-left align-middle font-semibold text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-900/50 [&:has([role=checkbox])]:pr-0 ${className || ''}`}
    {...props}
  />
))
TableHead.displayName = "TableHead"
 
const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
<td
    ref={ref}
    className={`px-6 py-4 align-middle text-gray-900 dark:text-gray-100 [&:has([role=checkbox])]:pr-0 ${className || ''}`}
    {...props}
  />
))
TableCell.displayName = "TableCell"
 
const TableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
<caption
    ref={ref}
    className={`mt-4 text-sm text-muted-foreground ${className || ''}`}
    {...props}
  />
))
TableCaption.displayName = "TableCaption"
 
export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}