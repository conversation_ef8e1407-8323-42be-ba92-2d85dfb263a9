import React from "react";
import View from "../view";
import Text from "../text";
import { ArchiveX } from "lucide-react";
import BouncingLoader from "../BouncingLoader";
import {
  Table,
  TableRow,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "./table";

interface DynamicTableProps {
  tableData: any[];
  tableHeaders: string[];
  isLoading?: boolean;
  emptyMessage?: string;
  emptyIcon?: React.ReactNode;
  renderCell?: (
    rowIndex: number,
    colIndex: number,
    value: any
  ) => React.ReactNode;
  getRowKey?: (row: any, rowIndex: number) => string | number;
  onRowClick?: (row: any, rowIndex: number) => void;
  header?: {
    search?: React.ReactNode;
    sort?: React.ReactNode;
    filter?: React.ReactNode;
    action?: React.ReactNode;
  };
  footer?: {
    pagination?: React.ReactNode;
  };
}

const DynamicTable: React.FC<DynamicTableProps> = ({
  tableData,
  tableHeaders,
  isLoading = false,
  emptyMessage = "No Data Found!",
  emptyIcon = (
    <ArchiveX className="w-10 h-10 mx-auto mb-2 bg-primary-100 p-2 rounded-full text-primary" />
  ),
  renderCell,
  getRowKey,
  onRowClick,
  header,
  footer,
}) => {
  return (
    <View>
      {/* Header controls */}
      {/* {(header?.search || header?.sort || header?.filter || header?.action) && ( */}
      <View className="p-8 border-b border-border bg-gradient-to-r from-primary-50 via-white to-secondary-50 dark:from-primary-900/10 dark:via-background dark:to-secondary-900/10 flex flex-col sm:flex-row gap-6 justify-between items-start sm:items-center">
        <View className="flex gap-2 w-full  justify-between items-center ">
          {header?.search}
          <View className="flex gap-3">
            {header?.sort}
            {header?.filter}
            {header?.action && (
              <View className="shrink-0">{header.action}</View>
            )}
          </View>
        </View>
      </View>
      {/* )} */}

      {/* Table */}
      <View className="overflow-x-auto">
        <Table className="w-full min-w-max">
          <TableHeader>
            <TableRow className="bg-gradient-to-r from-primary-100 via-primary-50 to-secondary-100 dark:from-primary-900/20 dark:via-primary-800/10 dark:to-secondary-900/20 border-b-2 border-primary-200 dark:border-primary-700">
              {tableHeaders.map((header, index) => (
                <TableHead
                  key={index}
                  className={`py-6 px-8 text-sm font-bold text-primary-800 dark:text-primary-300 tracking-wider uppercase letter-spacing-wide`}
                >
                  {header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={tableHeaders.length}
                  className="py-4 text-center relative"
                >
                  <View className="w-full z-50">
                    <BouncingLoader notFixed isLoading={isLoading} />
                  </View>
                </TableCell>
              </TableRow>
            ) : !tableData || tableData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={tableHeaders.length}
                  className="py-6 px-4 text-center text-text-light"
                >
                  {emptyIcon}
                  <Text as="span">{emptyMessage}</Text>
                </TableCell>
              </TableRow>
            ) : (
              tableData.map((row, rowIndex) => (
                <TableRow
                  key={getRowKey ? getRowKey(row, rowIndex) : rowIndex}
                  onClick={() => onRowClick?.(row, rowIndex)}
                  className={`border-b border-border hover:bg-gradient-to-r hover:from-primary-50/50 hover:to-secondary-50/50 dark:hover:from-primary-900/10 dark:hover:to-secondary-900/10 hover:shadow-md transition-all duration-300 ${
                    onRowClick ? "cursor-pointer" : ""
                  }`}
                >
                  {Array.isArray(row) &&
                    row.map((cell, colIndex) => (
                      <TableCell
                        key={"td_" + colIndex}
                        className={`py-6 px-8 text-foreground whitespace-nowrap font-medium`}
                      >
                        {renderCell
                          ? renderCell(rowIndex, colIndex, cell)
                          : cell}
                      </TableCell>
                    ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </View>

      {/* Footer (pagination) */}
      {footer?.pagination && <View className="pt-4">{footer.pagination}</View>}
    </View>
  );
};

export default DynamicTable;
