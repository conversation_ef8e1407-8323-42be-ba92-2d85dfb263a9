import View from "./view";
import Text from "./text";
import Button from "./button";
import React, { useEffect } from "react";

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  showCloseButton?: boolean;
  size?: "sm" | "md" | "lg" | "xl" | "full";
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  showCloseButton = true,
  size = "md",
}) => {
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    if (isOpen) {
      document.addEventListener("keydown", handleEsc);
    }
    return () => {
      document.removeEventListener("keydown", handleEsc);
    };
  }, [isOpen, onClose]);

  const sizeClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full mx-4",
  };

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <View
      role="dialog"
      aria-modal="true"
      onClick={handleBackdropClick}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm px-4"
    >
      <View
        className={`w-full ${sizeClasses[size]} bg-white dark:bg-gray-900 rounded-xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700 transform transition-all duration-200`}
      >
        <View className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-start">
          <View>
            <Text
              as="h2"
              className="text-xl font-bold text-gray-900 dark:text-white"
            >
              {title}
            </Text>
            {description && (
              <p className="text-gray-600 dark:text-gray-400 mt-1 text-sm">{description}</p>
            )}
          </View>
          {showCloseButton && (
            <Button
              variant="ghost"
              onClick={onClose}
              className="h-8 w-8 rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <Text as="span" className="text-lg leading-none">
                ×
              </Text>
            </Button>
          )}
        </View>

        <View className="p-6 overflow-y-auto" style={{ maxHeight: "70vh" }}>
          {children}
        </View>

        {footer && (
          <View className="bg-gray-50 dark:bg-gray-800/50 p-6 border-t border-gray-200 dark:border-gray-700">
            {footer}
          </View>
        )}
      </View>
    </View>
  );
};

export default Modal;
