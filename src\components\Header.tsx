import React from "react";
import { PanelRightClose } from "lucide-react";
import Button from "@/components/button";
import { Link } from "react-router-dom";
import { USER_PROFILE_URL } from "@/utils/urls/backend";
import { useSelector } from "react-redux";
import View from "./view";
import ImageComponent from "./ui/ImageComponent";
import Text from "./text";
// import { useSelector } from "react-redux";

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean
}




const Header: React.FC<HeaderProps> = ({ toggleSidebar,sidebarOpen }) => {
  const settingsData = useSelector((state: any) => state.systemSettings.settings);
  
  return (
    <header className="h-16 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 shadow-sm transition-colors duration-200">
      <View className="flex h-full items-center justify-between px-6">
        {
          !sidebarOpen && (
            <Button
          onClick={toggleSidebar}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-2"
          variant="ghost"
        >
          <PanelRightClose size={20} />
        </Button>
          )
        }

        {/* Search */}
        {/* <div className="hidden md:block relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-text-lighter" />
          </div>
          <input
            type="search"
            placeholder="Search..."
            className="pl-10 pr-4 py-2 w-full bg-neutral-100 border-none rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none"
          />
        </div> */}

        <View className="hidden md:block w-64">
          {/* <SearchBar /> */}
        </View>

        {/* User Menu & Notifications */}
        <View className="flex items-center gap-4">
          {/* Notifications */}
          <Button
            className="relative p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            variant="ghost"
          >
            <View className="w-5 h-5 rounded-full bg-gray-200 dark:bg-gray-600"></View>
            <View className="absolute top-1 right-1 h-2 w-2 rounded-full bg-blue-500"></View>
          </Button>

          {/* User Profile */}
          <Link to={USER_PROFILE_URL}>
            <Button
              className="flex items-center gap-3 px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
              variant="ghost"
            >
              {
                settingsData?.profile_image ? (
                  <View className="h-8 w-8 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-600">
                    <ImageComponent
                      src={import.meta.env.VITE_APP_URL + settingsData?.profile_image}
                      alt={"User profile image"}
                      className="rounded-full object-cover h-full w-full"
                    />
                  </View>
                ) : (
                  <View className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-500/20 flex items-center justify-center border-2 border-blue-200 dark:border-blue-500/30">
                    <Text as="span" className="text-sm font-medium text-blue-600 dark:text-blue-400">
                      U
                    </Text>
                  </View>
                )
              }
              <View className="hidden md:block">
                <Text as="span" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Profile
                </Text>
              </View>
            </Button>
          </Link>
        </View>
      </View>
    </header>
  );
};

export default Header;