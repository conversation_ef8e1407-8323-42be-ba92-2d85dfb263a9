import {
  BtnVariant,
  ButtonProps,
  SetButtonSizeProps,
} from "@/interfaces/components/button";

const setVariantCssHandler: Partial<Record<BtnVariant | "default", string>> = {
  default: "bg-indigo-600 hover:bg-indigo-700 text-white shadow-sm",
  ghost: "bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300",
  danger: "bg-red-600 hover:bg-red-700 text-white shadow-sm",
  primary: "bg-indigo-600 hover:bg-indigo-700 text-white shadow-sm",
  outline:
    "border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 shadow-sm",
};

const setButtonSize: SetButtonSizeProps = {
  large: "px-6 py-3 text-base",
  small: "px-3 py-2 text-sm",
  medium: "px-4 py-2.5 text-sm",
};
const Button: React.FC<ButtonProps> = ({
  style,
  onPress,
  className,
  size = "medium",
  loading = false,
  disabled = false,
  children = "Button",
  htmlType = "button",
  variant = "primary",
  ...props
}) => {
  //focus:ring-2 focus:ring-primary-300 focus:ring-offset-2
  return (
    <button
      type={htmlType}
      onClick={onPress}
      style={{ cursor: loading ? "progress" : "pointer", ...style }}
      disabled={disabled || loading}
      className={`
        ${loading ? "cursor-progress" : "cursor-pointer"}
        ${setButtonSize[size]}
        ${setVariantCssHandler[variant]}
        font-medium
        transition-all
        duration-200
        focus:outline-none
        focus:ring-2
        focus:ring-indigo-500
        focus:ring-offset-2
        dark:focus:ring-offset-gray-900
        rounded-lg
        disabled:opacity-50
        disabled:cursor-not-allowed
        ${className || ""}`}
      {...props}
    >
      {children}
    </button>
  );
};
export default Button;
