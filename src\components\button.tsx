import {
  BtnVariant,
  ButtonProps,
  SetButtonSizeProps,
} from "@/interfaces/components/button";

const setVariantCssHandler: Partial<Record<BtnVariant | "default", string>> = {
  default: "bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-primary-foreground shadow-lg hover:shadow-xl",
  ghost: "bg-transparent outline-none focus:outline-none hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 dark:hover:from-primary-900/20 dark:hover:to-secondary-900/20",
  danger: "bg-gradient-to-r from-destructive to-red-600 hover:from-red-600 hover:to-red-700 text-destructive-foreground shadow-lg hover:shadow-xl",
  primary: "bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-primary-foreground shadow-lg hover:shadow-xl",
  outline:
    "border-2 border-primary/30 bg-transparent hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 dark:hover:from-primary-900/20 dark:hover:to-secondary-900/20 text-foreground hover:border-primary",
};

const setButtonSize: SetButtonSizeProps = {
  large: "px-5 py-3 text-lg",
  small: "px-3 py-1.5 text-sm",
  medium: "px-4 py-2 text-base",
};
const Button: React.FC<ButtonProps> = ({
  style,
  onPress,
  className,
  size = "medium",
  loading = false,
  disabled = false,
  children = "Button",
  htmlType = "button",
  variant = "primary",
  ...props
}) => {
  //focus:ring-2 focus:ring-primary-300 focus:ring-offset-2
  return (
    <button
      type={htmlType}
      onClick={onPress}
      style={{ cursor: loading ? "progress" : "pointer", ...style }}
      disabled={disabled || loading}
      className={`${loading ? "progress" : "auto"} ${setButtonSize[size]} ${
        setVariantCssHandler[variant]
      } font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring/20 rounded-xl hover:scale-105 active:scale-95 ${className} `}
      {...props}
    >
      {children}
    </button>
  );
};
export default Button;
