import {
  BtnVariant,
  ButtonProps,
  SetButtonSizeProps,
} from "@/interfaces/components/button";

const setVariantCssHandler: Partial<Record<BtnVariant | "default", string>> = {
  default: "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-md",
  ghost: "bg-transparent outline-none focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-800",
  danger: "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-md",
  primary: "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white shadow-md",
  outline:
    "border border-gray-300 dark:border-gray-600 bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300",
};

const setButtonSize: SetButtonSizeProps = {
  large: "px-5 py-3 text-lg",
  small: "px-3 py-1.5 text-sm",
  medium: "px-4 py-2 text-base",
};
const Button: React.FC<ButtonProps> = ({
  style,
  onPress,
  className,
  size = "medium",
  loading = false,
  disabled = false,
  children = "Button",
  htmlType = "button",
  variant = "primary",
  ...props
}) => {
  //focus:ring-2 focus:ring-primary-300 focus:ring-offset-2
  return (
    <button
      type={htmlType}
      onClick={onPress}
      style={{ cursor: loading ? "progress" : "pointer", ...style }}
      disabled={disabled || loading}
      className={`${loading ? "progress" : "auto"} ${setButtonSize[size]} ${
        setVariantCssHandler[variant]
      } font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 rounded-lg ${className} `}
      {...props}
    >
      {children}
    </button>
  );
};
export default Button;
