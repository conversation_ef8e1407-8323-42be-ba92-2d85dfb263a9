import Text from "./text";
import View from "./view";
import React, { useEffect } from "react";
import But<PERSON> from "@/components/button";
import { useAuth } from "@/actions/calls/auth";
import { Link, useLocation } from "react-router-dom";
import { useWindowDimension } from "@/utils/custom-hooks/use-browser-dimentions";
import {
  Users,
  LogOut,
  UserPlus,
  // Menu,
  // Stethoscope,
  Settings,
  CalendarClock,
  LayoutDashboard,
  PanelRightOpen,
  ClipboardList,
  Table2,
  FileText,
  // Proportions,
  Wallet,
  Proportions,
} from "lucide-react";
import {
  SETTINGS_URL,
  DASHBOARD_URL,
  USER_TABLE_URL,
  PATIENT_TABLE_URL,
  // OPD_TABLE_URL,
  APPOINTMENT_TABLE_URL,
  CONSULTATION_TABLE_URL,
  ROLES_TABLE_URL,
  TEST_TABLE_URL,
  MEDICINE_TABLE_URL,
  ALLERGY_TABLE_URL,
  MEDICINE_CATEGORY_TABLE_URL,
  <PERSON><PERSON>_ANALYSIS_URL,
  PRAKRI<PERSON>_URL,
  VIKRUTI_URL,
  AG<PERSON>_URL,
  K<PERSON>HTA_URL,
  AVASTHA_URL,
  YOGA_ASANA_TABLE_URL,
  INVOICE_URL,
  DEPARTMENT_TABLE_URL,
  CONSULTATION_FEES_URL,
  SURGICAL_HISTORY_TABLE_URL,
  CHIEF_COMPLAINT_URL,
  ON_EXAMINATION_TABLE_URL,
  AMOUNT_TYPE_TABLE_URL,
  SERVICE_COST_TABLE_URL,
  COMORBIDITIES_TABLE_URL,
  DIET_TABLE_URL,
  // DIAGNOSIS_TABLE_URL,
  FOOD_ADVICE_TABLE_URL,
  EXPENSES_TABLE_URL,
  REPORT_EXPENSES,
  REPORT_INVOICE,
  DRE_TABLE_URL,
  PROCTOSCOPY_TABLE_URL,
  FISTULA_TABLE_URL,
  MANAGEMENT_TABLE_URL,
} from "@/utils/urls/frontend";
import { useSelector } from "react-redux";
// import { ExaminationsPage } from "@/pages/examinations/ExaminationsPage";
import {
  SidebarDropdown,
  SidebarDropdownItem,
} from "@/components/sidebar/index";
import dayjs from "dayjs";

interface SidebarItemProps {
  href?: string;
  label?: string;
  active?: boolean;
  icon?: React.ReactNode;
  isSideBarOpen: boolean;
  children?: React.ReactNode;
  onSelect?: () => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  href,
  active,
  isSideBarOpen,
  children,
  onSelect,
}) => {
  return (
    <React.Fragment>
      {children ? (
        // children
        children
      ) : (
        <Link
          to={href || "/"}
          onClick={onSelect}
          className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-200 ${
            active
              ? "bg-primary text-primary-foreground shadow-lg font-medium"
              : "text-foreground hover:bg-primary-bg hover:text-primary"
          }`}
        >
          <View className="text-current">{icon}</View>
          <Text as="span">{isSideBarOpen ? label : ""}</Text>
        </Link>
      )}
    </React.Fragment>
  );
};

interface SidebarProps {
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

export const sidebarItems = [
  {
    icon: <LayoutDashboard size={20} />,
    label: "Dashboard",
    href: DASHBOARD_URL,
  },
  {
    icon: <UserPlus size={20} />,
    label: "Patients",
    href: PATIENT_TABLE_URL + "?currentPage=1",
  },
  {
    icon: <CalendarClock size={20} />,
    label: "Appointments",
    href:
      APPOINTMENT_TABLE_URL +
      `?currentPage=1&from_date=${dayjs().format(
        "YYYY-MM-DD"
      )}&to_date=${dayjs().format("YYYY-MM-DD")}`,
  },
  {
    icon: <ClipboardList size={20} />,
    label: "Consultations",
    href: CONSULTATION_TABLE_URL + `?currentPage=1`,
    // href:
    //   CONSULTATION_TABLE_URL +
    //   `?currentPage=1&from_date=${dayjs().format(
    //     "YYYY-MM-DD"
    //   )}&to_date=${dayjs().format("YYYY-MM-DD")}`,
  },
  // ipd
  {
    icon: <FileText size={20} />,
    label: "Invoice",
    // href:
    //   INVOICE_URL +
    //   `?currentPage=1&from_date=${dayjs().format(
    //     "YYYY-MM-DD"
    //   )}&to_date=${dayjs().format("YYYY-MM-DD")}`,
    href: INVOICE_URL + `?currentPage=1`,
  },
  {
    icon: <Wallet size={20} />,
    label: "Expenses",
    href: EXPENSES_TABLE_URL + "?currentPage=1",
  },
  {
    icon: <Proportions size={20} />,
    label: "Expenses Report",
    children: (
      <SidebarDropdown
        title="Reports"
        icon={<Table2 size={20} />}
        variant="secondary"
      >
        <SidebarDropdownItem
          to={`${REPORT_EXPENSES}?currentPage=1&from_date=${dayjs().format(
            "YYYY-MM-DD"
          )}&to_date=${dayjs().format("YYYY-MM-DD")}`}
          label="Expenses Report"
        />
        <SidebarDropdownItem
          to={`${REPORT_INVOICE}?currentPage=1&from_date=${dayjs().format(
            "YYYY-MM-DD"
          )}&to_date=${dayjs().format("YYYY-MM-DD")}`}
          label="Invoice Report"
        />
      </SidebarDropdown>
    ),
    // href: REPORT_EXPENSES + "?currentPage=1",
  },
  // certificates
  {
    icon: <Table2 size={20} />,
    label: "Masters",
    children: (
      <SidebarDropdown
        title="Masters"
        icon={<Table2 size={20} />}
        variant="secondary"
      >
        {/* <SidebarDropdownItem to="/settings/general" label="General Settings" /> */}
        {/* <SidebarDropdownItem
          to={`${FINDINGS_URL}?currentPage=1`}
          label="Findings"
        /> */}
        <SidebarDropdownItem
          to={`${ROLES_TABLE_URL}?currentPage=1`}
          label="Roles"
        />
        <SidebarDropdownItem
          to={`${DEPARTMENT_TABLE_URL}?currentPage=1`}
          label="Departments"
        />
        {/* <SidebarDropdownItem
          to={`${ROOMS_TABLE_URL}?currentPage=1`}
          label="Rooms"
        /> */}
        <SidebarDropdownItem
          to={`${FISTULA_TABLE_URL}?currentPage=1`}
          label="Fistula"
        />
        <SidebarDropdownItem
          to={`${TEST_TABLE_URL}?currentPage=1`}
          label="Tests"
        />
        <SidebarDropdownItem
          to={`${AMOUNT_TYPE_TABLE_URL}?currentPage=1`}
          label="Amount Types"
        />
        <SidebarDropdownItem
          to={`${CONSULTATION_FEES_URL}?currentPage=1`}
          label="Consultation Fees"
        />
        <SidebarDropdownItem
          to={`${CHIEF_COMPLAINT_URL}?currentPage=1`}
          label="Chief Complaints"
        />
        <SidebarDropdownItem
          to={`${MEDICINE_CATEGORY_TABLE_URL}?currentPage=1`}
          label="Medicine Categories"
        />
        <SidebarDropdownItem
          to={`${MEDICINE_TABLE_URL}?currentPage=1`}
          label="Medicines"
        />
        <SidebarDropdownItem
          to={`${ALLERGY_TABLE_URL}?currentPage=1`}
          label="Allergies"
        />
        <SidebarDropdownItem
          to={`${SURGICAL_HISTORY_TABLE_URL}?currentPage=1`}
          label="Surgical History"
        />
        <SidebarDropdownItem
          to={`${ON_EXAMINATION_TABLE_URL}?currentPage=1`}
          label="On Examination"
        />
        <SidebarDropdownItem
          to={`${COMORBIDITIES_TABLE_URL}?currentPage=1`}
          label="Comorbidities"
        />
        <SidebarDropdownItem
          to={`${MANAGEMENT_TABLE_URL}?currentPage=1`}
          label="Managements"
        />
        <SidebarDropdownItem
          to={`${FOOD_ADVICE_TABLE_URL}?currentPage=1`}
          label="Food Advice"
        />

        {/* <SidebarDropdownItem to="/settings/general" label="General Settings" /> */}
        {/* <SidebarDropdownItem
          to={`${FINDINGS_URL}?currentPage=1`}
          label="Findings"
        /> */}
        <SidebarDropdownItem
          to={`${DOSHA_ANALYSIS_URL}${PRAKRITI_URL}?currentPage=1`}
          label="Prakriti"
        />
        <SidebarDropdownItem
          to={`${DOSHA_ANALYSIS_URL}${VIKRUTI_URL}?currentPage=1`}
          label="Vikruti"
        />
        <SidebarDropdownItem
          to={`${DOSHA_ANALYSIS_URL}${AGNI_URL}?currentPage=1`}
          label="Agni"
        />
        <SidebarDropdownItem
          to={`${DOSHA_ANALYSIS_URL}${KOSHTA_URL}?currentPage=1`}
          label="Koshta"
        />
        <SidebarDropdownItem
          to={`${DOSHA_ANALYSIS_URL}${AVASTHA_URL}?currentPage=1`}
          label="Avastha"
        />
        <SidebarDropdownItem
          to={`${YOGA_ASANA_TABLE_URL}?currentPage=1`}
          label="Yoga Asana"
        />
        <SidebarDropdownItem
          to={`${SERVICE_COST_TABLE_URL}?currentPage=1`}
          label="Service Costs"
        />
        <SidebarDropdownItem
          to={`${DIET_TABLE_URL}?currentPage=1`}
          label="Diets"
        />
        <SidebarDropdownItem
          to={`${DRE_TABLE_URL}?currentPage=1`}
          label="DRE"
        />
        <SidebarDropdownItem
          to={`${PROCTOSCOPY_TABLE_URL}?currentPage=1`}
          label="Proctoscopy"
        />
      </SidebarDropdown>
    ),
    // href: EXAMINATION_TABLE_URL + "?currentPage=1",
  },
  {
    icon: <Users size={20} />,
    label: "Users",
    href: USER_TABLE_URL + "?currentPage=1",
  },
  // {
  //   icon: <Stethoscope size={20} />,
  //   label: "OPD",
  //   href: OPD_TABLE_URL + "?currentPage=1",
  // },

  // {
  //   icon: <FlaskConical size={20} />,
  //   label: "Patient Tests",
  //   href: PATIENT_TEST_TABLE_URL + "?currentPage=1",
  // },
  {
    icon: <Settings size={20} />,
    label: "Settings",
    href: SETTINGS_URL + "?tab=system-settings",
  },
  // expenses
  // reports
];

const Sidebar: React.FC<SidebarProps> = ({ sidebarOpen, setSidebarOpen }) => {
  const location = useLocation();

  const [width] = useWindowDimension();
  const settingsData = useSelector(
    (state: any) => state.systemSettings.settings
  );

  useEffect(() => {
    if (width < 768) {
      setSidebarOpen(false);
    } else {
      setSidebarOpen(true);
    }
  }, [width]);

  const { logoutHandler } = useAuth();

  // const sidebarItems = [
  //   {
  //     icon: <LayoutDashboard size={20} />,
  //     label: "Dashboard",
  //     href: DASHBOARD_URL,
  //   },
  //   {
  //     icon: <Users size={20} />,
  //     label: "Users",
  //     href: USER_TABLE_URL + "?currentPage=1",
  //   },
  //   {
  //     icon: <UserPlus size={20} />,
  //     label: "Patients",
  //     href: PATIENT_TABLE_URL + "?currentPage=1",
  //   },
  //   // {
  //   //   icon: <Stethoscope size={20} />,
  //   //   label: "OPD",
  //   //   href: OPD_TABLE_URL + "?currentPage=1",
  //   // },
  //   {
  //     icon: <CalendarClock size={20} />,
  //     label: "Appointments",
  //     href: APPOINTMENT_TABLE_URL + "?currentPage=1",
  //   },
  //   {
  //     icon: <ClipboardList size={20} />,
  //     label: "Consultations",
  //     href: CONSULTATION_TABLE_URL + "?currentPage=1",
  //   },
  //   {
  //     icon: <Activity size={20} />,
  //     label: "Examinations",
  //     href: EXAMINATION_TABLE_URL + "?currentPage=1",
  //   },
  //   {
  //     icon: <Settings size={20} />,
  //     label: "Settings",
  //     href: SETTINGS_URL + "?tab=system-settings",
  //   },
  // ];

  return (
    sidebarOpen && (
      <aside
        className={`fixed inset-y-0 left-0 z-20 flex flex-col bg-card dark:bg-card border-r border-border dark:border-border shadow-card transition-all duration-300 md:static ${
          sidebarOpen
            ? width < 768
              ? "w-full"
              : "w-64"
            : "w-0 -translate-x-full md:w-20 md:translate-x-0"
        }`}
      >
        {/* Sidebar Header */}
        <View className="flex h-16 items-center justify-between gap-2 border-b border-border px-6 bg-primary-bg dark:bg-card">
          <Link to="/dashboard" className="flex items-center gap-2">
            {sidebarOpen ? (
              <Text
                as="h1"
                className="text-xl font-bold text-primary flex items-center gap-2"
                title={settingsData?.hospital_name}
              >
                <img
                  src={
                    import.meta.env.VITE_APP_URL + settingsData?.hospital_logo
                  }
                  alt="Hospital Logo"
                  className="mr-2 bg-card rounded-lg shadow-sm"
                  style={{ width: "180px", height: "36px", padding: "4px" }}
                />
              </Text>
            ) : (
              <View className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                <Text
                  as="span"
                  className="text-sm font-bold text-primary-600"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                >
                  M
                </Text>
              </View>
            )}
          </Link>
          <Button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-muted-foreground hover:text-primary p-2 rounded-lg hover:bg-primary-bg transition-all duration-200"
            variant="ghost"
          >
            <PanelRightOpen size={20} />
          </Button>
          {/* <Button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="hidden rounded-lg p-1.5 text-neutral-500 hover:bg-neutral-100 md:block "
            variant="ghost"
          >
            <X className={`${sidebarOpen ? "block" : "hidden"}`} size={20} />
          </Button> */}
        </View>

        {/* Sidebar Content */}
        <View className="flex-1 overflow-y-auto py-6 px-2">
          <nav className="space-y-2">
            {sidebarItems.map((item, index) => (
              <React.Fragment key={index}>
                <Text as="p">
                  {location.pathname.includes(item?.href ?? "")}
                </Text>
                {item?.children ? (
                  <SidebarItem
                    isSideBarOpen={sidebarOpen}
                    children={item.children}
                    onSelect={() => {
                      if (width < 768) {
                        setSidebarOpen(false);
                      }
                    }}
                  />
                ) : (
                  <SidebarItem
                    key={item.href}
                    icon={item.icon}
                    href={item.href}
                    label={item.label}
                    onSelect={() => {
                      if (width < 768) {
                        setSidebarOpen(false);
                      }
                    }}
                    isSideBarOpen={sidebarOpen}
                    active={location.pathname.includes(
                      item.href?.split("?")[0]
                    )}
                  />
                )}
              </React.Fragment>
            ))}
          </nav>
        </View>

        {/* Sidebar Footer */}
        <View className="border-t border-border p-4">
          <Button
            variant="ghost"
            onPress={() => {
              logoutHandler((_: boolean) => {});
            }}
            className="flex items-center gap-3 px-4 py-3 mx-2 rounded-xl text-foreground hover:bg-destructive/10 hover:text-destructive transition-all duration-200 w-full"
          >
            <LogOut size={20} />
            {sidebarOpen && <Text as="span">Logout</Text>}
          </Button>
        </View>
      </aside>
    )
  );
};

export default Sidebar;
