import View from "@/components/view";
import Modal from "@/components/Modal";
import { useDispatch, useSelector } from "react-redux";
import Button from "@/components/button";
import { Card } from "@/components/ui/card";
import { RootState } from "@/actions/store";
import { Plus } from "lucide-react";
import React, { useEffect, useState } from "react";
import SearchBar from "@/components/ui/search-bar";
import { usePatient } from "@/actions/calls/patient";
import ActionMenu from "@/components/editDeleteAction";
import PaginationComponent from "@/components/Pagination";
import { useNavigate, Link, useSearchParams, useLocation } from "react-router-dom";
import {
  PATIENT_TABLE_URL,
  PATIENTS_FORM_URL,
  PATIENT_DETAIL_URL,
} from "@/utils/urls/frontend";
import DataSort, { SortOption } from "@/components/SortData";
import DynamicTable from "@/components/ui/DynamicTable";
import { handleSortChange } from "@/utils/helperFunctions";
import Text from "@/components/text";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import Filter from "@/pages/filter/index";
import Input from "@/components/input";
import BouncingLoader from "@/components/BouncingLoader";
import { clearUserDetailsSlice } from "@/actions/slices/patient";
const PatientsPage: React.FC<{}> = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const { cleanUp, patientListHandler, deletePatientHandler } = usePatient();
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );
  const dispatch = useDispatch();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const paginateObj = useSelector(
    (state: RootState) => state.patient.userCompleteObj
  );

  useEffect(() => {
    if (location.state?.refresh || searchParams.has("currentPage")) {
      patientListHandler(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        filterData,
        (status) => {
          setIsLoading(status === "pending" ? true : status === "failed" ? true : status === "success" && false);
        }
      );
    }
    return () => {
      cleanUp();
      dispatch(clearUserDetailsSlice());
    };
  }, [
    filterData,
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const handleDeletePatient = () => {
    if (deleteId) {
      deletePatientHandler(deleteId, (success: boolean) => {
        if (success) {
          patientListHandler(searchParams?.get("currentPage") ?? 1, () => {
            modalCloseHandler();
          });
        }
      });
    }
  };

  const sortOptions: SortOption[] = [
    { label: "Name (A-Z)", value: "first_name", order: "asc" },
    { label: "Name (Z-A)", value: "first_name", order: "desc" },
    { label: "PatientID (A-Z)", value: "patient_number", order: "asc" },
    { label: "PatientID (Z-A)", value: "patient_number", order: "desc" },
    { label: "Age (A-Z)", value: "age", order: "asc" },
    { label: "Age (Z-A)", value: "age", order: "desc" },
    { label: "Phone (A-Z)", value: "phone_no", order: "asc" },
    { label: "Phone (Z-A)", value: "phone_no", order: "desc" },
    { label: "Status (A-Z)", value: "status", order: "asc" },
    { label: "Status (Z-A)", value: "status", order: "desc" },
  ];

  const [activeSort, setActiveSort] = useState<SortOption | null>(
    sortOptions[0]
  );

  // const handleSortChange = (option: SortOption) => {
  //   setActiveSort(option);
  //   if(option.order){
  //     setSearchParams(
  //       {
  //         ...Object.fromEntries([...searchParams]),
  //         currentPage: "1",
  //         sort_by:  option.value,
  //         sort_order: option.order,
  //       },
  //       { replace: true }
  //     );
  //   }
  // };

  return (
    <React.Fragment>
      <BouncingLoader isLoading={isLoading} />
      {deleteId && (
        <Modal
          title="Patient Delete"
          isOpen={deleteId ? true : false}
          onClose={modalCloseHandler}
          description="Are you sure you want to delete this Patient? This action cannot be undone and will permanently remove the patient's data from the system."
        >
          <View className="flex justify-end gap-2">
            <Button variant="outline" onPress={modalCloseHandler}>
              Cancel
            </Button>
            <Button variant="danger" onPress={handleDeletePatient}>
              Delete
            </Button>
          </View>
        </Modal>
      )}
      <View className="mb-6">
        <Text
          as="h1"
          weight="font-semibold"
          className="text-2xl font-bold text-text-DEFAULT mb-1"
        >
          Patients
        </Text>
        <Text as="p" className="text-text-light">
          Manage patient records and information
        </Text>
      </View>

      {/* Stats Cards */}
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card className="p-4 flex flex-col bg-card shadow-sm dark:shadow-md">
          <Text as="span" className="text-sm text-text-light mb-1">
            Total Patients
          </Text>
          <Text
            as="span"
            weight="font-bold"
            className="!text-3xl font-bold text-success"
          >
            {paginateObj?.patient?.total}
          </Text>
        </Card>
        <Card className="p-4 flex flex-col bg-card shadow-sm dark:shadow-md">
          <Text as="span" className="text-sm text-text-light mb-1">
            Active Patients
          </Text>
          <Text
            as="span"
            weight="font-bold"
            className="!text-3xl font-bold text-success"
          >
            {paginateObj?.active_patient}
          </Text>
        </Card>
      </View>

      <Card className="overflow-hidden dark:shadow-md">
        <DynamicTable
          tableHeaders={[
            "Patient ID",
            // "OPD Number",
            "Name",
            "Age",
            "Phone",
            "Email",
            // "Diatary Preference",
            "Status",
            "Actions",
          ]}
          tableData={paginateObj?.patient?.data?.map((patient: any) => [
            patient.patient_number,
            // patient?.opd_number ?? "NA",
            <View className="flex items-center">
              <View className="h-8 w-8 rounded-full bg-secondary-50 flex items-center justify-center mr-3">
                <Text
                  as="span"
                  className="text-xs font-medium text-secondary-600"
                >
                  {patient.first_name.charAt(0)}
                  {patient.last_name.charAt(0)}
                </Text>
              </View>
              <Link
                to={PATIENT_TABLE_URL + PATIENT_DETAIL_URL + "/" + patient.id}
              >
                <Text
                  as="span"
                  className="font-medium text-text-DEFAULT hover:text-secondary hover:underline"
                >
                  {patient.first_name + " " + patient.last_name}
                </Text>
              </Link>
            </View>,
            patient.age,
            patient.phone_no,
            patient.email ?? "-",
            // patient.dietary_preference,
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full `}
              style={getStatusColorScheme(patient?.status)}
            >
              {patient.status}
            </Text>,
            <ActionMenu
            onView={() =>
                navigate(
                  PATIENT_TABLE_URL + PATIENT_DETAIL_URL + "/" + patient.id
                )
              }
              onEdit={() =>
                navigate(
                  PATIENT_TABLE_URL + PATIENTS_FORM_URL + "/" + patient.id
                )
              }
              onDelete={() => {
                setDeleteId(patient.id);
              }}
            />,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                      search: value,
                    },
                    { replace: true }
                  );
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            filter: (
              <Filter
                onResetFilter={() => {
                  setFilterData(null);
                }}
                title="Findings Filter"
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="first_name" placeholder="Patient First Name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="last_name" placeholder="Patient Last Name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="gender" placeholder="Gender" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="age" placeholder="Age" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="country" placeholder="Country" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="created_at" placeholder="Created Date" />
                  </View>,

                  // <View className="w-full my-4">
                  //   <Input name="phone_no" placeholder="Patient Phone" />
                  // </View>,
                ]}
              />
            ),
            action: (
              <Button
                variant="primary"
                size="small"
                className="flex items-center gap-2 "
                onPress={() => navigate(PATIENT_TABLE_URL + PATIENTS_FORM_URL)}
              >
                <Plus size={16} />
                Add Patient
              </Button>
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={paginateObj?.patient?.current_page}
                last_page={paginateObj?.patient?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
    </React.Fragment>
  );
};

export default PatientsPage;
