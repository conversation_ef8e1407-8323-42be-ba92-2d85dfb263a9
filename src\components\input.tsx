// Input component
import { InputProps } from "@/interfaces/components/input/input";
import View from "./view";

interface CSSProps {
  small: string;
  medium: string;
  large: string;
}
interface VariantProps {
  default: string;
  filled: string;
  error: string;
  outlined: string;
}

const sizeClasses: CSSProps = {
  small: "h-8 text-xs px-2 py-1",
  medium: "h-10 text-sm px-3 py-2",
  large: "h-12 text-base px-4 py-3",
};

const setVariantHandler: VariantProps = {
  outlined: "border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800",
  error: "border-red-500 focus:ring-red-500/30 bg-white dark:bg-gray-800",
  default:
    "border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100",
  filled:
    "border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100",
};

const Input: React.FC<InputProps> = ({
  id,
  ref,
  name,
  style,
  error,
  value,
  label,
  onBlur,
  onChange,
  leftIcon,
  rightIcon,
  className,
  type = "text",
  disabled = false,
  fullWidth = false,
  variant = "default",
  inputSize = "medium",
  placeholder = "Enter text",
  required = false,
  ...rest
}) => {
  return (
    <View className="relative w-full">
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <View className={`relative ${fullWidth ? "w-full" : ""}`}>
        {leftIcon && (
          <View className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <View className="text-gray-400 dark:text-gray-500">{leftIcon}</View>
          </View>
        )}
        <input
          id={id}
          ref={ref}
          name={name}
          type={type}
          value={value?.toString()}
          style={style}
          onBlur={onBlur}
          autoComplete="off"
          onChange={onChange}
          disabled={disabled}
          placeholder={placeholder}
          className={`
            w-full
            px-3
            py-2.5
            rounded-lg
            focus:outline-none
            focus:ring-2
            focus:ring-indigo-500
            focus:border-indigo-500
            dark:focus:ring-indigo-400
            dark:focus:border-indigo-400
            transition-all
            duration-200
            placeholder-gray-400
            dark:placeholder-gray-500
            disabled:opacity-50
            disabled:cursor-not-allowed
            ${setVariantHandler[variant]}
            ${leftIcon ? "pl-10" : ""}
            ${rightIcon ? "pr-10" : ""}
            ${error ? "border-red-500 focus:ring-red-500 focus:border-red-500" : ""}
            ${className || ""}
          `}
          {...rest}
        />
        {rightIcon && (
          <View className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <View className="text-gray-400 dark:text-gray-500">{rightIcon}</View>
          </View>
        )}
      </View>
      {error && (
        <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
          {error}
        </p>
      )}
    </View>
  );
};

export default Input;
