{"hash": "2e6179f9", "configHash": "a762cc2d", "lockfileHash": "a4ff48be", "browserHash": "c31ce589", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "82b61847", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "bfdc03eb", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "940bdb2b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "3bda543b", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "e35957cf", "needsInterop": false}, "@tiptap/extension-text-align": {"src": "../../@tiptap/extension-text-align/dist/index.js", "file": "@tiptap_extension-text-align.js", "fileHash": "fe1cfc72", "needsInterop": false}, "@tiptap/react": {"src": "../../@tiptap/react/dist/index.js", "file": "@tiptap_react.js", "fileHash": "9a2b8164", "needsInterop": false}, "@tiptap/starter-kit": {"src": "../../@tiptap/starter-kit/dist/index.js", "file": "@tiptap_starter-kit.js", "fileHash": "35a2efaf", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "fb9fd487", "needsInterop": false}, "dayjs": {"src": "../../dayjs/dayjs.min.js", "file": "dayjs.js", "fileHash": "b3094425", "needsInterop": true}, "jwt-decode": {"src": "../../jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "430dbba2", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "82e29d8e", "needsInterop": false}, "react-day-picker": {"src": "../../react-day-picker/dist/esm/index.js", "file": "react-day-picker.js", "fileHash": "daa3aa8b", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "a1a08903", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "92afa3d0", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "bbb081a1", "needsInterop": false}, "yup": {"src": "../../yup/index.esm.js", "file": "yup.js", "fileHash": "fb4b80bb", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "f863703d", "needsInterop": false}}, "chunks": {"chunk-76BQHZKB": {"file": "chunk-76BQHZKB.js"}, "chunk-OX56QJAV": {"file": "chunk-OX56QJAV.js"}, "chunk-RUPNRBO7": {"file": "chunk-RUPNRBO7.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}