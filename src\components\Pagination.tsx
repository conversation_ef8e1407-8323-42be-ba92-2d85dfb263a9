import React from "react";
import But<PERSON> from "./button";
import View from "./view";

interface PaginationProps {
  last_page?: number;
  current_page?: number;
  getPageNumberHandler?: (pageNuber: number) => void;
}

const PaginationComponent: React.FC<PaginationProps> = ({
  last_page = 1,
  current_page = 1,
  getPageNumberHandler,
}) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= last_page && getPageNumberHandler) {
      getPageNumberHandler(page);
    }
  };

  return (
    <View>
      {/* <ul>
        {items.map((item, idx) => (
          <li key={idx}>{item.name}</li> // adjust based on your model
        ))}
      </ul> */}

      <View className="flex items-center gap-3 justify-end py-4">
        <Button
          disabled={current_page === 1}
          onPress={() => handlePageChange(current_page - 1)}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-card border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          Previous
        </Button>

        {[...Array(last_page)].map((_, index) => {
          const pageNum = index + 1;
          const isActive = pageNum === current_page;
          return (
            <Button
              variant="ghost"
              key={pageNum}
              onPress={() => handlePageChange(pageNum)}
              className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                isActive
                  ? "bg-blue-600 text-white shadow-md"
                  : "text-gray-700 dark:text-gray-300 bg-white dark:bg-card border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
              }`}
            >
              {pageNum}
            </Button>
          );
        })}

        <Button
          disabled={current_page === last_page}
          onPress={() => handlePageChange(current_page + 1)}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-card border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          Next
        </Button>
      </View>
    </View>
  );
};

export default PaginationComponent;
